/*
###
Description:    Batch compress existing images by checking size and compressing if needed

Usage:         ./start.sh -n compressPic -d "goresodownload" -cmd "cmd/batch/compressPic/main.go -dir=/path/to/images -size=200 -dryrun"

Create date:    2025-08-22
Author:         <PERSON><PERSON> x<PERSON>owei
Run frequency:  One-off or as needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gofile "github.com/real-rm/gofile"
	golog "github.com/real-rm/golog"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	"github.com/real-rm/gostreaming"
)

const (
	// Minimum concurrent workers
	MinConcurrentWorkers = 1
	// Maximum concurrent workers
	MaxConcurrentWorkers = 100
)

var (
	dirFlag             = flag.String("dir", "", "Directory path to process images (required)")
	sizeFlag            = flag.Int("size", 200, "Target size limit in KB (default: 200)")
	recursiveFlag       = flag.Bool("recursive", true, "Process subdirectories recursively (default: true)")
	dryrunFlag          = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	concurrentFlag      = flag.Int("concurrent", 1, "Number of concurrent processing goroutines (default: 1)")
	continueOnErrorFlag = flag.Bool("continue-on-error", true, "Continue processing other files when encountering errors (default: true)")

	speedMeter *gospeedmeter.SpeedMeter
	startTime  = time.Now()
)

// Supported image extension
var supportedExtensions = map[string]bool{
	".jpg":  true,
	".jpeg": true,
	".png":  true,
	".webp": true,
}

// Statistics for processing
type ProcessStats struct {
	TotalFiles      int64
	ProcessedFiles  int64
	CompressedFiles int64
	SkippedFiles    int64
	ErrorFiles      int64
	TotalSizeBefore int64
	TotalSizeAfter  int64
	mutex           sync.Mutex
}

// FileStream implements streaming interface for file scanning
type FileStream struct {
	dirPath   string
	recursive bool
	fileChan  chan string
	err       error
	done      bool
	current   string
	ctx       context.Context
	cancel    context.CancelFunc
}

// NewFileStream creates a new file stream for the given directory
func NewFileStream(ctx context.Context, dirPath string, recursive bool) *FileStream {
	streamCtx, cancel := context.WithCancel(ctx)
	fs := &FileStream{
		dirPath:   dirPath,
		recursive: recursive,
		fileChan:  make(chan string, 100), // Small buffer for smooth streaming
		ctx:       streamCtx,
		cancel:    cancel,
	}

	// Start scanning in a separate goroutine
	go fs.scan()

	return fs
}

// scan performs the actual file scanning in a separate goroutine
func (fs *FileStream) scan() {
	defer close(fs.fileChan)

	if fs.recursive {
		err := filepath.WalkDir(fs.dirPath, func(path string, d os.DirEntry, err error) error {
			if err != nil {
				golog.Warn("Error accessing path", "path", path, "error", err)
				return nil // Continue processing other files
			}

			// Check for context cancellation
			select {
			case <-fs.ctx.Done():
				return fs.ctx.Err()
			default:
			}

			if !d.IsDir() && isImageFile(d.Name()) {
				select {
				case fs.fileChan <- path:
				case <-fs.ctx.Done():
					return fs.ctx.Err()
				}
			}
			return nil
		})
		if err != nil && err != context.Canceled {
			fs.err = err
		}
	} else {
		// Non-recursive: only scan the specified directory
		entries, err := os.ReadDir(fs.dirPath)
		if err != nil {
			fs.err = fmt.Errorf("failed to read directory %s: %w", fs.dirPath, err)
			return
		}

		for _, entry := range entries {
			// Check for context cancellation
			select {
			case <-fs.ctx.Done():
				fs.err = fs.ctx.Err()
				return
			default:
			}

			if !entry.IsDir() && isImageFile(entry.Name()) {
				fullPath := filepath.Join(fs.dirPath, entry.Name())
				select {
				case fs.fileChan <- fullPath:
				case <-fs.ctx.Done():
					fs.err = fs.ctx.Err()
					return
				}
			}
		}
	}
}

// Next implements the streaming interface - returns true if there's a next item
func (fs *FileStream) Next(ctx context.Context) bool {
	if fs.done {
		return false
	}

	select {
	case filePath, ok := <-fs.fileChan:
		if !ok {
			fs.done = true
			return false
		}
		fs.current = filePath
		return true
	case <-ctx.Done():
		fs.err = ctx.Err()
		fs.done = true
		return false
	}
}

// Decode implements the streaming interface - decodes the current item
func (fs *FileStream) Decode(v interface{}) error {
	if fs.current == "" {
		return fmt.Errorf("no current item to decode")
	}

	// Handle different pointer types that gostreaming might pass
	switch ptr := v.(type) {
	case *string:
		*ptr = fs.current
		return nil
	case *interface{}:
		*ptr = fs.current
		return nil
	default:
		return fmt.Errorf("unsupported decode type %T, expected *string or *interface{}", v)
	}
}

// Err implements the streaming interface - returns any error that occurred
func (fs *FileStream) Err() error {
	return fs.err
}

// Close closes the file stream and cancels the scanning goroutine
func (fs *FileStream) Close() {
	fs.cancel()
	fs.done = true
}

func (s *ProcessStats) AddFile(sizeBefore, sizeAfter int64, compressed bool, hasError bool) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.TotalFiles++
	s.TotalSizeBefore += sizeBefore

	if hasError {
		s.ErrorFiles++
		return
	}

	s.ProcessedFiles++
	s.TotalSizeAfter += sizeAfter

	if compressed {
		s.CompressedFiles++
	} else {
		s.SkippedFiles++
	}
}

func (s *ProcessStats) GetStats() (int64, int64, int64, int64, int64, int64, int64) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	return s.TotalFiles, s.ProcessedFiles, s.CompressedFiles, s.SkippedFiles, s.ErrorFiles, s.TotalSizeBefore, s.TotalSizeAfter
}

func init() {
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

// isImageFile checks if the file has a supported image extension
func isImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return supportedExtensions[ext]
}

// getFileSizeBytes returns file size in bytes
func getFileSizeBytes(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// bytesToKB converts bytes to KB with proper rounding
func bytesToKB(bytes int64) float64 {
	return float64(bytes) / 1024.0
}

// processImageFile processes a single image file
func processImageFile(filePath string, targetSizeKB int, stats *ProcessStats) error {
	speedMeter.Check("processed", 1)

	// Get original file size in bytes
	originalSizeBytes, err := getFileSizeBytes(filePath)
	if err != nil {
		golog.Error("Failed to get file size", "file", filePath, "error", err)
		stats.AddFile(0, 0, false, true) // Keep as 0,0 since we don't know the file size
		return fmt.Errorf("failed to get file size for %s: %w", filePath, err)
	}

	originalSizeKB := bytesToKB(originalSizeBytes)
	targetSizeBytes := int64(targetSizeKB) * 1024

	golog.Debug("Processing image", "file", filePath, "originalSizeKB", fmt.Sprintf("%.2f", originalSizeKB), "targetSizeKB", targetSizeKB)

	// Check if compression is needed (compare in bytes for accuracy)
	if originalSizeBytes <= targetSizeBytes {
		golog.Debug("Skipping file - already within target size", "file", filePath, "sizeKB", fmt.Sprintf("%.2f", originalSizeKB))
		stats.AddFile(originalSizeBytes, originalSizeBytes, false, false)
		speedMeter.Check("skipped", 1)
		return nil
	}

	if *dryrunFlag {
		golog.Info("Dry run: would compress image", "file", filePath, "originalSizeKB", fmt.Sprintf("%.2f", originalSizeKB), "targetSizeKB", targetSizeKB)
		stats.AddFile(originalSizeBytes, originalSizeBytes, false, false) // Fixed: compressed=false for dry run
		speedMeter.Check("dryrun", 1)
		return nil
	}

	// Read file data
	imageData, err := os.ReadFile(filePath)
	if err != nil {
		golog.Error("Failed to read image file", "file", filePath, "error", err)
		stats.AddFile(originalSizeBytes, originalSizeBytes, false, true) // Fixed: file size unchanged on error
		return fmt.Errorf("failed to read image file %s: %w", filePath, err)
	}

	// Process image with compression
	processStart := time.Now()
	processedData, err := gofile.ProcessImageDataWithResize(imageData, targetSizeKB)
	processDuration := time.Since(processStart)

	if err != nil {
		golog.Error("Failed to process image", "file", filePath, "duration", processDuration, "error", err)
		stats.AddFile(originalSizeBytes, originalSizeBytes, false, true) // Fixed: file size unchanged on error
		return fmt.Errorf("failed to process image %s: %w", filePath, err)
	}

	// Get original file info to preserve permissions
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		golog.Error("Failed to get file info", "file", filePath, "error", err)
		stats.AddFile(originalSizeBytes, originalSizeBytes, false, true) // Fixed: file size unchanged on error
		return fmt.Errorf("failed to get file info for %s: %w", filePath, err)
	}

	// Write processed data back to file with original permissions
	err = os.WriteFile(filePath, processedData, fileInfo.Mode())
	if err != nil {
		golog.Error("Failed to write processed image", "file", filePath, "error", err)
		stats.AddFile(originalSizeBytes, originalSizeBytes, false, true) // Fixed: file size unchanged on error
		return fmt.Errorf("failed to write processed image %s: %w", filePath, err)
	}

	newSizeBytes := int64(len(processedData))
	newSizeKB := bytesToKB(newSizeBytes)
	compressionRatio := float64(len(processedData)) / float64(len(imageData))

	golog.Info("Successfully compressed image",
		"file", filePath,
		"originalSizeKB", fmt.Sprintf("%.2f", originalSizeKB),
		"newSizeKB", fmt.Sprintf("%.2f", newSizeKB),
		"compressionRatio", fmt.Sprintf("%.2f", compressionRatio),
		"duration", processDuration)

	stats.AddFile(originalSizeBytes, newSizeBytes, true, false)
	speedMeter.Check("compressed", 1)

	return nil
}

// printStats prints processing statistics
func printStats(stats *ProcessStats) {
	total, processed, compressed, skipped, errors, sizeBefore, sizeAfter := stats.GetStats()

	golog.Info("====== Processing Statistics ======")
	golog.Info("Files processed", "total", total, "processed", processed, "compressed", compressed, "skipped", skipped, "errors", errors)

	if sizeBefore > 0 {
		savedBytes := sizeBefore - sizeAfter
		savedMB := float64(savedBytes) / (1024 * 1024)
		compressionRatio := float64(sizeAfter) / float64(sizeBefore)

		golog.Info("Size statistics",
			"sizeBefore_MB", fmt.Sprintf("%.2f", float64(sizeBefore)/(1024*1024)),
			"sizeAfter_MB", fmt.Sprintf("%.2f", float64(sizeAfter)/(1024*1024)),
			"saved_MB", fmt.Sprintf("%.2f", savedMB),
			"compressionRatio", fmt.Sprintf("%.2f", compressionRatio))
	}

	duration := time.Since(startTime)
	golog.Info("Processing completed", "duration", duration.String(), "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
}

// validateInputs validates command line arguments
func validateInputs() error {
	if *dirFlag == "" {
		return fmt.Errorf("directory path is required (use -dir flag)")
	}

	// Clean and validate directory path
	cleanDir := filepath.Clean(*dirFlag)
	*dirFlag = cleanDir

	// Check if directory exists
	if _, err := os.Stat(*dirFlag); os.IsNotExist(err) {
		return fmt.Errorf("directory does not exist: %s", *dirFlag)
	}

	// Validate target size
	if *sizeFlag <= 0 {
		return fmt.Errorf("target size must be positive, got: %d", *sizeFlag)
	}

	// Validate concurrent workers
	if *concurrentFlag < MinConcurrentWorkers {
		golog.Warn("Concurrent workers too low, setting to minimum", "requested", *concurrentFlag, "minimum", MinConcurrentWorkers)
		*concurrentFlag = MinConcurrentWorkers
	}
	if *concurrentFlag > MaxConcurrentWorkers {
		golog.Warn("Concurrent workers too high, setting to maximum", "requested", *concurrentFlag, "maximum", MaxConcurrentWorkers)
		*concurrentFlag = MaxConcurrentWorkers
	}

	return nil
}

// run executes the main processing logic using streaming
func run(ctx context.Context) error {
	flag.Parse()

	// Validate inputs
	if err := validateInputs(); err != nil {
		return fmt.Errorf("input validation failed: %w", err)
	}

	golog.Info("Starting image compression batch",
		"directory", *dirFlag,
		"targetSizeKB", *sizeFlag,
		"recursive", *recursiveFlag,
		"dryrun", *dryrunFlag,
		"concurrent", *concurrentFlag,
		"continueOnError", *continueOnErrorFlag)

	// Initialize statistics
	stats := &ProcessStats{}

	// Create file stream
	golog.Info("Starting streaming file processing...")
	fileStream := NewFileStream(ctx, *dirFlag, *recursiveFlag)
	defer fileStream.Close()

	// Create streaming options
	opts := &gostreaming.StreamingOptions{
		Stream:        fileStream,
		High:          *concurrentFlag,
		SpeedInterval: 5000, // Log speed every 5 seconds
		Verbose:       1,    // Enable basic logging
		Event:         "image_processing",
		Process: func(item interface{}) error {
			// Extract file path from the item (item is already decoded by gostreaming)
			filePath, ok := item.(string)
			if !ok {
				return fmt.Errorf("expected string file path, got %T", item)
			}

			// Process the image file
			err := processImageFile(filePath, *sizeFlag, stats)
			if err != nil {
				if !*continueOnErrorFlag {
					return fmt.Errorf("processing failed for %s: %w", filePath, err)
				}
				// Log error but continue processing when continueOnErrorFlag is true
				golog.Error("Failed to process file, continuing", "file", filePath, "error", err)
			}
			return nil
		},
		Error: func(err error) {
			golog.Error("Streaming error occurred", "error", err)
		},
		End: func(err error) {
			if err != nil {
				golog.Error("Streaming ended with error", "error", err)
			} else {
				golog.Info("Streaming completed successfully")
			}
		},
	}

	// Start streaming processing
	err := gostreaming.Streaming(ctx, opts)
	if err != nil {
		return fmt.Errorf("streaming processing failed: %w", err)
	}

	// Print final statistics
	printStats(stats)

	return nil
}

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	golog.Info("Starting compressPic batch")
	if err := run(ctx); err != nil {
		golog.Fatal("compressPic failed", "error", err)
	}
	golog.Info("compressPic completed successfully")
}
